'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, CheckCircle, Copy, Image as ImageIcon, Upload, X } from 'lucide-react';
import React, { useCallback, useRef, useState } from 'react';
import { toast } from 'sonner';

interface UploadedImage {
  file: File;
  preview: string;
  url?: string;
}

export default function ImageUploadPage() {
  const [uploadedImage, setUploadedImage] = useState<UploadedImage | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return 'Please select a valid image file (JPG, PNG, GIF, or WebP)';
    }
    if (file.size > maxFileSize) {
      return 'File size must be less than 10MB';
    }
    return null;
  };

  const uploadToAPI = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => Math.min(prev + 10, 90));
    }, 200);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      if (!result.success || !result.url) {
        throw new Error('Invalid response from upload API');
      }

      return result.url;
    } catch (error) {
      clearInterval(progressInterval);
      throw error;
    }
  };

  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    const preview = URL.createObjectURL(file);
    setUploadedImage({ file, preview });
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const url = await uploadToAPI(file);
      setUploadedImage((prev) => (prev ? { ...prev, url } : null));
      toast.success('Image uploaded successfully!');
    } catch (err) {
      setError('Failed to upload image. Please try again.');
      toast.error('Upload failed');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Link copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy link');
    }
  };

  const clearUpload = () => {
    if (uploadedImage?.preview) {
      URL.revokeObjectURL(uploadedImage.preview);
    }
    setUploadedImage(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="container mx-auto max-w-4xl p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Image Upload</h1>
        <p className="text-muted-foreground">
          Upload your images to apiupanh.knowledgehub.io.vn and get direct links instantly
        </p>
      </div>

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Image
          </CardTitle>
          <CardDescription>Drag and drop an image here, or click to select a file</CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`
              relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
              ${isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
              ${
                isUploading
                  ? 'pointer-events-none opacity-50'
                  : 'hover:border-primary hover:bg-primary/5 cursor-pointer'
              }
            `}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept={allowedTypes.join(',')}
              onChange={handleFileInputChange}
              className="hidden"
            />

            <div className="space-y-4">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <ImageIcon className="h-6 w-6 text-primary" />
              </div>

              <div className="space-y-2">
                <p className="text-lg font-medium">
                  {isDragOver ? 'Drop your image here' : 'Choose an image to upload'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Supports JPG, PNG, GIF, and WebP files up to 10MB
                </p>
              </div>

              <Button variant="outline" disabled={isUploading}>
                Select File
              </Button>
            </div>
          </div>

          {isUploading && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} />
            </div>
          )}

          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Image Preview and Link Display */}
      {uploadedImage && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Upload Complete
              </CardTitle>
              <Button variant="ghost" size="icon" onClick={clearUpload}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <CardDescription>Your image has been uploaded successfully</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Image Preview */}
            <div className="space-y-2">
              <h3 className="font-medium">Preview</h3>
              <div className="relative max-w-md mx-auto">
                <img
                  src={uploadedImage.preview}
                  alt="Uploaded preview"
                  className="w-full h-auto rounded-lg border shadow-sm"
                />
              </div>
              <div className="text-sm text-muted-foreground text-center">
                {uploadedImage.file.name} ({(uploadedImage.file.size / 1024 / 1024).toFixed(2)} MB)
              </div>
            </div>

            {/* Generated Link */}
            {uploadedImage.url && (
              <div className="space-y-2">
                <h3 className="font-medium">Shareable Link</h3>
                <div className="flex gap-2">
                  <Input value={uploadedImage.url} readOnly className="font-mono text-sm" />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => copyToClipboard(uploadedImage.url!)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
