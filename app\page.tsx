'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, CheckCircle, Copy, Image as ImageIcon, Upload, X } from 'lucide-react';
import React, { useCallback, useRef, useState } from 'react';
import { toast } from 'sonner';

interface UploadedImage {
  id: string;
  file: File;
  preview: string;
  url?: string;
  uploadedAt: Date;
}

interface SavedImage {
  id: string;
  fileName: string;
  fileSize: number;
  url: string;
  uploadedAt: string;
}

export default function ImageUploadPage() {
  const [uploadedImage, setUploadedImage] = useState<UploadedImage | null>(null);
  const [uploadedImages, setUploadedImages] = useState<SavedImage[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  // Load saved images from localStorage on component mount
  React.useEffect(() => {
    const saved = localStorage.getItem('uploadedImages');
    if (saved) {
      try {
        setUploadedImages(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading saved images:', error);
      }
    }
  }, []);

  // Save to localStorage whenever uploadedImages changes
  React.useEffect(() => {
    localStorage.setItem('uploadedImages', JSON.stringify(uploadedImages));
  }, [uploadedImages]);

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return 'Vui lòng chọn file ảnh có định dạng (JPG, PNG, GIF, or WebP)';
    }
    if (file.size > maxFileSize) {
      return 'Dung lượng file không được vượt quá 10MB';
    }
    return null;
  };

  const uploadToAPI = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => Math.min(prev + 10, 90));
    }, 200);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Có lỗi khi upload ảnh');
      }

      const result = await response.json();

      if (!result.success || !result.url) {
        throw new Error('Có lỗi khi upload ảnh');
      }

      return result.url;
    } catch (error) {
      clearInterval(progressInterval);
      throw error;
    }
  };

  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    const preview = URL.createObjectURL(file);
    const imageId = Date.now().toString();
    const newImage: UploadedImage = {
      id: imageId,
      file,
      preview,
      uploadedAt: new Date(),
    };

    setUploadedImage(newImage);
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const url = await uploadToAPI(file);
      setUploadedImage((prev) => (prev ? { ...prev, url } : null));

      // Add to uploaded images list
      const savedImage: SavedImage = {
        id: imageId,
        fileName: file.name,
        fileSize: file.size,
        url,
        uploadedAt: new Date().toISOString(),
      };

      setUploadedImages((prev) => [savedImage, ...prev]);
      toast.success('Ảnh đã được upload!');
    } catch (err) {
      setError('Có lỗi khi upload ảnh vui lòng thử lại sau!');
      toast.error('Có lỗi khi upload ảnh!');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, []);

  // Handle paste events for Ctrl+V
  React.useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            handleFileSelect(file);
            e.preventDefault();
            toast.success('ảnh đã được dán từ clipboard!');
            break;
          }
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Link được copy vào clipboard!');
    } catch (err) {
      toast.error('Lỗi khi copy link');
    }
  };

  const clearUpload = () => {
    if (uploadedImage?.preview) {
      URL.revokeObjectURL(uploadedImage.preview);
    }
    setUploadedImage(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const deleteImage = (id: string) => {
    setUploadedImages((prev) => prev.filter((img) => img.id !== id));
    toast.success('Ảnh được xóa khỏi list');
  };

  const clearAllImages = () => {
    setUploadedImages([]);
    toast.success('Đã xóa tất cả ảnh');
  };

  return (
    <div className="container mx-auto max-w-4xl p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Up Ảnh Lấy Link</h1>
        <p className="text-muted-foreground">
          Upload ảnh của bạn để lấy link - web được tạo bởi Minh Hoàng
        </p>
      </div>

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload ảnh
          </CardTitle>
          <CardDescription>
            Kéo và thả ảnh vào đây, nhấp để chọn một tệp, hoặc nhấn Ctrl+V để dán từ bộ nhớ tạm
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`
              relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
              ${isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
              ${
                isUploading
                  ? 'pointer-events-none opacity-50'
                  : 'hover:border-primary hover:bg-primary/5 cursor-pointer'
              }
            `}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept={allowedTypes.join(',')}
              onChange={handleFileInputChange}
              className="hidden"
            />

            <div className="space-y-4">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <ImageIcon className="h-6 w-6 text-primary" />
              </div>

              <div className="space-y-2">
                <p className="text-lg font-medium">
                  {isDragOver ? 'Drop your image here' : 'Choose an image to upload'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Hỗ trợ JPG, PNG, GIF, and WebP files lên tới 10MB
                </p>
                <p className="text-xs text-muted-foreground">
                  💡 Tip: Bạn có thể dùng Ctrl+V để dán ảnh
                </p>
              </div>

              <Button variant="outline" disabled={isUploading}>
                Chọn Ảnh
              </Button>
            </div>
          </div>

          {isUploading && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} />
            </div>
          )}

          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Image Preview and Link Display */}
      {uploadedImage && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Upload Thành Công
              </CardTitle>
              <Button variant="ghost" size="icon" onClick={clearUpload}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <CardDescription>Ảnh đã được upload xong và sẵn sàng để copy</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Image Preview */}
            <div className="space-y-2">
              <h3 className="font-medium">Preview</h3>
              <div className="relative max-w-md mx-auto">
                <img
                  src={uploadedImage.preview}
                  alt="Uploaded preview"
                  className="w-full h-auto rounded-lg border shadow-sm"
                />
              </div>
              <div className="text-sm text-muted-foreground text-center">
                {uploadedImage.file.name} ({(uploadedImage.file.size / 1024 / 1024).toFixed(2)} MB)
              </div>
            </div>

            {/* Generated Link */}
            {uploadedImage.url && (
              <div className="space-y-2">
                <h3 className="font-medium">Chia sẻ link</h3>
                <div className="flex gap-2">
                  <Input value={uploadedImage.url} readOnly className="font-mono text-sm" />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => copyToClipboard(uploadedImage.url!)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Uploaded Images History */}
      {uploadedImages.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Ảnh đã upload ({uploadedImages.length})
              </CardTitle>
              <Button variant="outline" size="sm" onClick={clearAllImages}>
                Clear All
              </Button>
            </div>
            <CardDescription>Tất cả ảnh đã được upload</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadedImages.map((image) => (
                <div
                  key={image.id}
                  className="flex items-center gap-4 p-3 border rounded-lg hover:bg-muted/50"
                >
                  <div className="w-16 h-16 rounded-lg overflow-hidden border bg-muted">
                    <img
                      src={image.url}
                      alt={image.fileName}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{image.fileName}</p>
                    <p className="text-sm text-muted-foreground">
                      {(image.fileSize / 1024 / 1024).toFixed(2)} MB •{' '}
                      {new Date(image.uploadedAt).toLocaleString()}
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={() => copyToClipboard(image.url)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => deleteImage(image.id)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
