/**
 * Upload an image file and get a direct link
 * @param file - The image file to upload
 * @returns Promise that resolves to the direct image URL
 */
export async function uploadImage(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Upload failed');
  }

  const result = await response.json();
  
  if (!result.success || !result.url) {
    throw new Error('Invalid response from upload API');
  }

  return result.url;
}

/**
 * Upload multiple images and get direct links
 * @param files - Array of image files to upload
 * @returns Promise that resolves to an array of direct image URLs
 */
export async function uploadImages(files: File[]): Promise<string[]> {
  const uploadPromises = files.map(file => uploadImage(file));
  return Promise.all(uploadPromises);
}

/**
 * Validate if a file is an image
 * @param file - The file to validate
 * @returns boolean indicating if the file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Validate file size (default max 10MB)
 * @param file - The file to validate
 * @param maxSizeInMB - Maximum file size in MB (default: 10)
 * @returns boolean indicating if the file size is valid
 */
export function isValidFileSize(file: File, maxSizeInMB: number = 10): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
}
