'use client';

import { useState } from 'react';
import { uploadImage } from '@/lib/upload';

/**
 * Simple example component showing how to use the upload utility
 */
export function SimpleUploadExample() {
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    setError(null);
    setImageUrl(null);

    try {
      const url = await uploadImage(file);
      setImageUrl(url);
      console.log('Image uploaded successfully:', url);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      console.error('Upload error:', err);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg space-y-4">
      <h3 className="text-lg font-semibold">Simple Upload Example</h3>
      
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        disabled={uploading}
        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
      />

      {uploading && (
        <p className="text-blue-600">Uploading...</p>
      )}

      {error && (
        <p className="text-red-600">Error: {error}</p>
      )}

      {imageUrl && (
        <div className="space-y-2">
          <p className="text-green-600">Upload successful!</p>
          <p className="text-sm break-all">URL: {imageUrl}</p>
          <img src={imageUrl} alt="Uploaded" className="max-w-xs h-auto border rounded" />
        </div>
      )}
    </div>
  );
}
