import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');

    // Get the form data from the request
    const formData = await request.formData();
    const file = formData.get('file') as File;

    console.log('File received:', file?.name, file?.size);

    if (!file) {
      console.log('No file provided');
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Create a new FormData object to send to the external API
    const uploadFormData = new FormData();
    uploadFormData.append('image', file); // API expects 'image' field name

    console.log('Uploading to external API...');
    console.log('File type:', file.type);
    console.log('File name:', file.name);

    // Upload to the external API
    const response = await fetch('http://apiupanh.knowledgehub.io.vn', {
      method: 'POST',
      body: uploadFormData,
      headers: {
        // Don't set Content-Type, let the browser set it with boundary
      },
    });

    console.log('External API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('External API error:', errorText);
      throw new Error(`Upload failed with status: ${response.status} - ${errorText}`);
    }

    // Get the response from the external API
    const result = await response.text();
    console.log('External API response:', result);

    // Return the direct link
    return NextResponse.json({
      success: true,
      url: result.trim(), 
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      {
        error: 'Upload failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
