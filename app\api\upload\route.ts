import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Get the form data from the request
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Create a new FormData object to send to the external API
    const uploadFormData = new FormData();
    uploadFormData.append('file', file);

    // Upload to the external API
    const response = await fetch('http://apiupanh.knowledgehub.io.vn', {
      method: 'POST',
      body: uploadFormData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    // Get the response from the external API
    const result = await response.text();
    
    // Return the direct link
    return NextResponse.json({
      success: true,
      url: result.trim(), // Trim any whitespace from the response
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { 
        error: 'Upload failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
