# Image Upload API Integration

This project integrates with `http://apiupanh.knowledgehub.io.vn` to upload images and get direct links.

## Files Created

### 1. API Route: `app/api/upload/route.ts`
- Next.js API route that handles image uploads
- Accepts FormData with a `file` field
- Forwards the file to the external API
- Returns the direct image URL

### 2. Utility Functions: `lib/upload.ts`
- `uploadImage(file: File)` - Upload a single image
- `uploadImages(files: File[])` - Upload multiple images
- `isImageFile(file: File)` - Validate if file is an image
- `isValidFileSize(file: File, maxSizeInMB: number)` - Validate file size

### 3. React Components
- `components/image-upload.tsx` - Full-featured upload component
- `components/simple-upload-example.tsx` - Simple example component
- Updated `app/page.tsx` - Main page with upload functionality

## Usage Examples

### Basic Upload with Utility Function

```typescript
import { uploadImage } from '@/lib/upload';

const handleUpload = async (file: File) => {
  try {
    const imageUrl = await uploadImage(file);
    console.log('Image uploaded:', imageUrl);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

### Using the API Route Directly

```typescript
const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  });

  const result = await response.json();
  return result.url; // Direct image URL
};
```

### Using the React Component

```tsx
import { ImageUpload } from '@/components/image-upload';

function MyComponent() {
  return (
    <ImageUpload
      onUploadSuccess={(url) => console.log('Uploaded:', url)}
      onUploadError={(error) => console.error('Error:', error)}
      maxSizeInMB={10}
    />
  );
}
```

## API Response Format

The external API returns a plain text response containing the direct image URL.

Our internal API wraps this in a JSON response:

```json
{
  "success": true,
  "url": "https://direct-image-url.com/image.jpg"
}
```

## Error Handling

The API handles various error cases:
- No file provided
- Upload failures
- Invalid responses from external API

Errors are returned in this format:

```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

## File Validation

The utility functions include validation for:
- File type (must be an image)
- File size (default max 10MB, configurable)

## Development

To test the upload functionality:

1. Start the development server:
   ```bash
   pnpm dev
   ```

2. Navigate to the main page to test the upload interface

3. Or use the utility functions in your own components

## Notes

- The external API endpoint is: `http://apiupanh.knowledgehub.io.vn`
- Files are sent as FormData with the field name `file`
- The response is a direct image URL that can be used immediately
- No authentication is required for the external API
